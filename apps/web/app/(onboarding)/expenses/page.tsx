'use client'

import { useRouter } from 'next/navigation'
import { OnboardingStepHeader } from '../components'
import { useOnboardingStore, useCanNavigate, useOnboardingNavigation } from '../hooks'

// TODO: Allow advisor to prefill expense estimates when client is invited to onboard
export default function MonthlyExpensesPage() {
  const router = useRouter()
  const { canGoBack } = useCanNavigate()
  const { getNextStepPath, getPreviousStepPath } = useOnboardingNavigation()
  const { markStepComplete } = useOnboardingStore()

  const handleSave = () => {
    // TODO: Implement save draft functionality for monthly expenses
    console.log('Saving monthly expenses draft...')
    markStepComplete('expenses')
  }

  const handleContinue = () => {
    const nextPath = getNextStepPath()
    if (nextPath) {
      router.push(nextPath)
    }
  }

  const handleBack = () => {
    const prevPath = getPreviousStepPath()
    if (prevPath) {
      router.push(prevPath)
    }
  }

  return (
    <div>
      <OnboardingStepHeader
        title="Expenses"
        description="Monthly spending and costs"
        showBack={canGoBack}
        onBack={handleBack}
        onSave={handleSave}
        onContinue={handleContinue}
      />

      <div className="space-y-8">
        {/* Placeholder for monthly expenses form */}
        <div className="bg-blue-50 p-6 rounded-lg border border-blue-200">
          <h2 className="text-lg font-semibold mb-4">📊 Monthly Expenses Form</h2>
          <p className="text-muted-foreground">
            This will contain form fields for housing, transportation, food, utilities, entertainment, and other expenses.
          </p>
        </div>
      </div>
    </div>
  )
}