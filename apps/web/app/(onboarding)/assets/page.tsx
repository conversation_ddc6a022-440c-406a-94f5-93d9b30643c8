'use client'

import { useRouter } from 'next/navigation'
import { OnboardingStepHeader } from '../components'
import { useOnboardingStore, useCanNavigate, useOnboardingNavigation } from '../hooks'

export default function AssetsPage() {
  const router = useRouter()
  const { canGoBack } = useCanNavigate()
  const { getNextStepPath, getPreviousStepPath } = useOnboardingNavigation()
  const { markStepComplete } = useOnboardingStore()

  const handleSave = () => {
    console.log('Saving assets draft...')
    markStepComplete('assets')
  }

  const handleContinue = () => {
    const nextPath = getNextStepPath()
    if (nextPath) {
      router.push(nextPath)
    }
  }

  const handleBack = () => {
    const prevPath = getPreviousStepPath()
    if (prevPath) {
      router.push(prevPath)
    }
  }

  return (
    <div>
      <OnboardingStepHeader
        title="Assets"
        description="Your investments, savings, and valuable assets"
        showBack={canGoBack}
        onBack={handleBack}
        onSave={handleSave}
        onContinue={handleContinue}
      />

      <div className="space-y-8">
        <div className="bg-blue-50 p-6 rounded-lg border border-blue-200">
          <h2 className="text-lg font-semibold mb-4">💰 Assets Form</h2>
          <p className="text-muted-foreground">
            This will contain form fields for checking/savings accounts, investment accounts, retirement funds, and other valuable assets.
          </p>
        </div>
      </div>
    </div>
  )
}