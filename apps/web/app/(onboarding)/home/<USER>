'use client'

import { useRouter } from 'next/navigation'
import { HomeForm } from '../components'
import { useOnboardingStore, useCanNavigate, useOnboardingNavigation } from '../hooks'
import { HomeInfoData } from '../types'

export default function HomePage() {
  const router = useRouter()
  const { canGoBack } = useCanNavigate()
  const { getNextStepPath, getPreviousStepPath } = useOnboardingNavigation()
  const { data, updateData, markStepComplete } = useOnboardingStore()

  const handleSave = (formData: Partial<HomeInfoData>) => {
    updateData('homeInfo', formData)
    markStepComplete('home')
  }

  const handleContinue = (formData: HomeInfoData) => {
    updateData('homeInfo', formData)
    markStepComplete('home')
    
    const nextPath = getNextStepPath()
    if (nextPath) {
      router.push(nextPath)
    }
  }

  const handleBack = () => {
    const prevPath = getPreviousStepPath()
    if (prevPath) {
      router.push(prevPath)
    }
  }

  return (
    <HomeForm
      initialData={data.homeInfo}
      onBack={canGoBack ? handleBack : undefined}
      onSave={handleSave}
      onContinue={handleContinue}
    />
  )
}