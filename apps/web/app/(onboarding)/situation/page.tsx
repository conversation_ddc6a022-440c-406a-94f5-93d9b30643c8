'use client'

import { useRouter } from 'next/navigation'
import { SituationForm } from '../components'
import { useOnboardingStore, useCanNavigate, useOnboardingNavigation } from '../hooks'
import { SituationData } from '../types'

export default function SituationPage() {
  const router = useRouter()
  const { canGoBack } = useCanNavigate()
  const { getNextStepPath, getPreviousStepPath } = useOnboardingNavigation()
  const { data, updateData, markStepComplete } = useOnboardingStore()

  const handleSave = (formData: Partial<SituationData>) => {
    updateData('situation', formData)
    markStepComplete('situation')
  }

  const handleContinue = (formData: SituationData) => {
    updateData('situation', formData)

    const nextPath = getNextStepPath()
    if (nextPath) {
      router.push(nextPath)
    }
  }

  const handleBack = () => {
    const prevPath = getPreviousStepPath()
    if (prevPath) {
      router.push(prevPath)
    }
  }

  return (
    <SituationForm
      initialData={data.situation}
      onBack={canGoBack ? handleBack : undefined}
      onSave={handleSave}
      onContinue={handleContinue}
    />
  )
}