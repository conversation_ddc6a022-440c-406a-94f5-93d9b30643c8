'use client'

import { useRouter } from 'next/navigation'
import { OnboardingStepHeader } from '../components'
import { useOnboardingStore, useCanNavigate, useOnboardingNavigation } from '../hooks'

// TODO: Allow advisor to prefill income sources when client is invited to onboard
export default function IncomeSourcesPage() {
  const router = useRouter()
  const { canGoBack } = useCanNavigate()
  const { getNextStepPath, getPreviousStepPath } = useOnboardingNavigation()
  const { markStepComplete } = useOnboardingStore()

  const handleSave = () => {
    // TODO: Implement save draft functionality for income sources
    console.log('Saving income sources draft...')
    markStepComplete('income')
  }

  const handleContinue = () => {
    markStepComplete('income')
    const nextPath = getNextStepPath()
    if (nextPath) {
      router.push(nextPath)
    }
  }

  const handleBack = () => {
    const prevPath = getPreviousStepPath()
    if (prevPath) {
      router.push(prevPath)
    }
  }

  return (
    <div>
      <OnboardingStepHeader
        title="Income"
        description="Tell us about your sources of income"
        showBack={canGoBack}
        onBack={handleBack}
        onSave={handleSave}
        onContinue={handleContinue}
      />

      <div className="space-y-8">
        {/* Placeholder for income sources form */}
        <div className="bg-blue-50 p-6 rounded-lg border border-blue-200">
          <h2 className="text-lg font-semibold mb-4">💰 Income Sources Form</h2>
          <p className="text-muted-foreground">
            This will contain form fields for salary, bonuses, investments, rental income, etc.
          </p>
        </div>
      </div>
    </div>
  )
}