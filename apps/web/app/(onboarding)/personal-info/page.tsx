'use client'

import { useRouter } from 'next/navigation'
import { PersonalInfoForm } from '../components'
import { useOnboardingStore, useCanNavigate, useOnboardingNavigation } from '../hooks'
import { PersonalInfoData } from '../types'

// TODO: Allow advisor to prefill personal info fields when client is invited to onboard
export default function PersonalInfoPage() {
  const router = useRouter()
  const { canGoBack } = useCanNavigate()
  const { getNextStepPath, getPreviousStepPath } = useOnboardingNavigation()
  const { data, updateData, markStepComplete } = useOnboardingStore()

  const handleSave = (formData: Partial<PersonalInfoData>) => {
    updateData('personalInfo', formData)
    markStepComplete('personal-info')
  }

  const handleContinue = (formData: PersonalInfoData) => {
    updateData('personalInfo', formData)
    markStepComplete('personal-info')
    
    const nextPath = getNextStepPath()
    if (nextPath) {
      router.push(nextPath)
    }
  }

  const handleBack = () => {
    const prevPath = getPreviousStepPath()
    if (prevPath) {
      router.push(prevPath)
    }
  }

  return (
    <PersonalInfoForm
      initialData={data.personalInfo}
      onBack={canGoBack ? handleBack : undefined}
      onSave={handleSave}
      onContinue={handleContinue}
    />
  )
}