'use client'

import { useRouter } from 'next/navigation'
import { OnboardingStepHeader } from '../components'
import { useOnboardingStore, useCanNavigate, useOnboardingNavigation } from '../hooks'

export default function InsurancePage() {
  const router = useRouter()
  const { canGoBack } = useCanNavigate()
  const { getNextStepPath, getPreviousStepPath } = useOnboardingNavigation()
  const { markStepComplete } = useOnboardingStore()

  const handleSave = () => {
    console.log('Saving insurance draft...')
    markStepComplete('insurance')
  }

  const handleContinue = () => {
    const nextPath = getNextStepPath()
    if (nextPath) {
      router.push(nextPath)
    }
  }

  const handleBack = () => {
    const prevPath = getPreviousStepPath()
    if (prevPath) {
      router.push(prevPath)
    }
  }

  return (
    <div>
      <OnboardingStepHeader
        title="Insurance"
        description="Insurance policies and coverage information"
        showBack={canGoBack}
        onBack={handleBack}
        onSave={handleSave}
        onContinue={handleContinue}
      />

      <div className="space-y-8">
        <div className="bg-blue-50 p-6 rounded-lg border border-blue-200">
          <h2 className="text-lg font-semibold mb-4">🛡️ Insurance Form</h2>
          <p className="text-muted-foreground">
            This will contain form fields for health, life, disability, auto, and other insurance coverage details.
          </p>
        </div>
      </div>
    </div>
  )
}