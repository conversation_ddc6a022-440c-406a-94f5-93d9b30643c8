'use client'

import { useRouter } from 'next/navigation'
import { OnboardingStepHeader } from '../components'
import { useOnboardingStore, useCanNavigate, useOnboardingNavigation } from '../hooks'

// TODO: Generate comprehensive financial summary and next steps
export default function ReviewCompletePage() {
  const router = useRouter()
  const { canGoBack } = useCanNavigate()
  const { getPreviousStepPath } = useOnboardingNavigation()
  const { markStepComplete, completedSteps } = useOnboardingStore()

  const handleSave = () => {
    // TODO: Final submission and account setup completion
    console.log('Completing onboarding process...')
    markStepComplete('review')
  }

  const handleComplete = () => {
    // TODO: Redirect to main dashboard after onboarding completion
    console.log('Finalizing onboarding and redirecting to dashboard...')
    markStepComplete('review')
    // router.push('/dashboard')
  }

  const handleBack = () => {
    const prevPath = getPreviousStepPath()
    if (prevPath) {
      router.push(prevPath)
    }
  }

  return (
    <div>
      <OnboardingStepHeader
        title="Review"
        description="Review and complete your profile"
        showBack={canGoBack}
        onBack={handleBack}
        onSave={handleSave}
        onContinue={handleComplete}
        continueText="Complete Setup →"
      />

      <div className="space-y-8">
        {/* Progress Summary */}
        <div className="bg-green-50 p-6 rounded-lg border border-green-200">
          <h2 className="text-lg font-semibold mb-4">✅ Onboarding Progress</h2>
          <p className="text-muted-foreground mb-4">
            You&apos;ve completed {completedSteps.size} out of 9 steps in your financial profile setup.
          </p>
          <div className="text-sm text-green-700">
            <strong>Completed Steps:</strong> {Array.from(completedSteps).join(', ')}
          </div>
        </div>

        {/* Review Summary */}
        <div className="bg-blue-50 p-6 rounded-lg border border-blue-200">
          <h2 className="text-lg font-semibold mb-4">📋 Review Summary</h2>
          <p className="text-muted-foreground">
            This will show a comprehensive review of all entered information, financial calculations, 
            and recommended next steps for your financial journey.
          </p>
        </div>

        {/* Next Steps */}
        <div className="bg-purple-50 p-6 rounded-lg border border-purple-200">
          <h2 className="text-lg font-semibold mb-4">🎯 What&apos;s Next?</h2>
          <p className="text-muted-foreground">
            After completing setup, you&apos;ll be redirected to your personalized financial dashboard 
            with insights, recommendations, and tools to achieve your financial goals.
          </p>
        </div>
      </div>
    </div>
  )
}