import { Button } from '@finpro/ui'

interface OnboardingStepHeaderProps {
  title: string
  description?: string
  showBack?: boolean
  showSave?: boolean
  showContinue?: boolean
  onBack?: () => void
  onSave?: () => void
  onContinue?: () => void
  continueText?: string
  isLoading?: boolean
  isSaveDisabled?: boolean
  isContinueDisabled?: boolean
}

export function OnboardingStepHeader({
  title,
  description,
  showBack = false,
  showSave = true,
  showContinue = true,
  onBack,
  onSave,
  onContinue,
  continueText = "Continue →",
  isLoading = false,
  isSaveDisabled = false,
  isContinueDisabled = false
}: OnboardingStepHeaderProps) {
  return (
    <div className="flex items-start justify-between mb-8">
      <div>
        <h1 className="text-2xl font-bold text-foreground mb-2">{title}</h1>
        {description && (
          <p className="text-sm text-muted-foreground">{description}</p>
        )}
      </div>
      
      <div className="flex items-center gap-3">
        {showBack && onBack && (
          <Button variant="outlined" onClick={onBack}>
            ← Back
          </Button>
        )}
        
        {showSave && onSave && (
          <Button 
            variant="ghost" 
            onClick={onSave}
            disabled={isSaveDisabled || isLoading}
          >
            Save Draft
          </Button>
        )}
        
        {showContinue && onContinue && (
          <Button 
            onClick={onContinue}
            disabled={isContinueDisabled || isLoading}
          >
            {isLoading ? "Loading..." : continueText}
          </Button>
        )}
      </div>
    </div>
  )
}