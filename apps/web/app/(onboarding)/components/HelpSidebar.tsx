import { OnboardingStepId } from '../types'
import { STEP_HELP_CONTENT, HelpSection } from '../lib/helpContent'

interface HelpSidebarProps {
  currentStep: OnboardingStepId
}

function HelpSectionComponent({ section, isExpanded = true }: { section: HelpSection, isExpanded?: boolean }) {
  return (
    <div className="mb-6">
      <h4 className="font-semibold text-foreground mb-2 flex items-center gap-2 text-sm">
        {section.title}
      </h4>
      <p className="text-sm text-muted-foreground mb-3 leading-relaxed">
        {section.content}
      </p>
      {section.tips && section.tips.length > 0 && isExpanded && (
        <div className="space-y-2">
          <div className="text-xs font-medium text-muted-foreground/80 uppercase tracking-wide mb-1">
            💡 Tips
          </div>
          {section.tips.map((tip, index) => (
            <div key={index} className="flex items-start gap-2 text-xs text-muted-foreground bg-accent/20 rounded p-2">
              <span className="text-primary mt-0.5 flex-shrink-0">•</span>
              <span className="leading-relaxed">{tip}</span>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

export function HelpSidebar({ currentStep }: HelpSidebarProps) {
  const helpContent = STEP_HELP_CONTENT[currentStep]

  if (!helpContent) {
    return (
      <div className="p-4">
        <h3 className="font-semibold text-foreground mb-2">Need Help?</h3>
        <p className="text-sm text-muted-foreground">
          Complete this step to continue your financial profile setup.
        </p>
      </div>
    )
  }

  return (
    <div className="p-4 h-full overflow-y-auto">
      <div className="mb-6">
        <h3 className="font-semibold text-foreground mb-2">{helpContent.title}</h3>
        <p className="text-sm text-muted-foreground leading-relaxed">
          {helpContent.description}
        </p>
      </div>

      <div className="space-y-6">
        {helpContent.sections.map((section, index) => (
          <HelpSectionComponent key={index} section={section} />
        ))}
      </div>

      {/* Quick Contact */}
      <div className="mt-8 p-3 bg-accent/10 rounded-lg border border-accent/20">
        <h4 className="font-medium text-foreground mb-2 text-sm">Need More Help?</h4>
        <p className="text-xs text-muted-foreground mb-2">
          Our financial advisors are here to help with any questions.
        </p>
        <button className="text-xs text-primary hover:text-primary/80 font-medium">
          Contact Support →
        </button>
      </div>
    </div>
  )
}