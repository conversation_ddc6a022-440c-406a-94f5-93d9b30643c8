import { useState } from 'react'
import { Input, Form, FormField, FormLabel, FormControl, FormMessage, Button, Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from '@finpro/ui'
import { HomeInfoData } from '../../types'
import { OnboardingStepHeader } from '../OnboardingStepHeader'

interface HomeFormProps {
  initialData?: Partial<HomeInfoData>
  onBack?: () => void
  onSave?: (data: Partial<HomeInfoData>) => void
  onContinue: (data: HomeInfoData) => void
}

export function HomeForm({ initialData, onBack, onSave, onContinue }: HomeFormProps) {
  const [formData, setFormData] = useState<HomeInfoData>({
    housingStatus: initialData?.housingStatus || null,
    // Owner fields
    address: initialData?.address || '',
    estimatedValue: initialData?.estimatedValue || 0,
    dateAcquired: initialData?.dateAcquired || '',
    // Renter fields
    rentPayment: initialData?.rentPayment || 0,
    rentInterval: initialData?.rentInterval || 'monthly'
  })
  
  const [errors, setErrors] = useState<Record<string, string>>({})

  const handleInputChange = (field: keyof HomeInfoData) => (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = e.target.type === 'number' 
      ? parseFloat(e.target.value) || 0 
      : e.target.value
    
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const handleFieldUpdate = (field: keyof HomeInfoData, value: HomeInfoData[keyof HomeInfoData]) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // Clear error when field is updated
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const handleHousingStatusChange = (status: 'own' | 'rent') => {
    handleFieldUpdate('housingStatus', status)
  }

  // Custom validation that considers housing status
  const validateHomeForm = (): boolean => {
    const newErrors: Record<string, string> = {}
    
    if (!formData.housingStatus) {
      newErrors.housingStatus = 'Please select your housing status'
    }

    // Additional conditional validation
    if (formData.housingStatus === 'own') {
      if (!formData.address?.trim()) {
        newErrors.address = 'Property address is required'
      }
      if (!formData.estimatedValue || formData.estimatedValue <= 0) {
        newErrors.estimatedValue = 'Estimated value is required'
      }
      if (!formData.dateAcquired?.trim()) {
        newErrors.dateAcquired = 'Date acquired is required'
      }
    }

    if (formData.housingStatus === 'rent') {
      if (!formData.rentPayment || formData.rentPayment <= 0) {
        newErrors.rentPayment = 'Rent payment is required'
      }
      if (!formData.rentInterval) {
        newErrors.rentInterval = 'Payment interval is required'
      }
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const getAnnualRentCost = () => {
    if (!formData.rentPayment || !formData.rentInterval) return 0
    
    switch (formData.rentInterval) {
      case 'weekly':
        return formData.rentPayment * 52
      case 'monthly':
        return formData.rentPayment * 12
      case 'yearly':
        return formData.rentPayment
      default:
        return 0
    }
  }
  
  const annualRentCost = getAnnualRentCost()

  const handleSave = () => {
    if (onSave) {
      onSave(formData)
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    handleContinue()
  }

  const handleContinue = () => {
    if (validateHomeForm()) {
      onContinue(formData as HomeInfoData)
    }
  }

  return (
    <div>
      <OnboardingStepHeader
        title="Primary Residence"
        description="Tell us about your housing situation"
        showBack={!!onBack}
        onBack={onBack}
        onSave={onSave ? handleSave : undefined}
        onContinue={handleContinue}
      />

      <Form asChild>
        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-12 gap-8 min-h-[600px]">
            {/* Left Side - Housing Status Selection */}
            <div className="col-span-4">
              <FormField name="housingStatus">
                <FormLabel required className="text-lg font-semibold mb-4 block">
                  What is your housing situation?
                </FormLabel>
                <div className="space-y-4">
                  <Button
                    type="button"
                    variant={formData.housingStatus === 'own' ? 'default' : 'outlined'}
                    className="w-full h-auto p-6 flex flex-col items-center justify-center gap-4 min-h-[120px]"
                    onClick={() => handleHousingStatusChange('own')}
                  >
                    <svg 
                      width="52" 
                      height="52" 
                      viewBox="0 0 24 24" 
                      fill="none" 
                      stroke="currentColor" 
                      strokeWidth="1.5" 
                      className="lucide lucide-home"
                    >
                      <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
                      <polyline points="9,22 9,12 15,12 15,22"/>
                    </svg>
                    <div className="font-semibold text-base text-center leading-tight">I own my home</div>
                  </Button>
                  
                  <Button
                    type="button"
                    variant={formData.housingStatus === 'rent' ? 'default' : 'outlined'}
                    className="w-full h-auto p-6 flex flex-col items-center justify-center gap-4 min-h-[120px]"
                    onClick={() => handleHousingStatusChange('rent')}
                  >
                    <svg 
                      width="52" 
                      height="52" 
                      viewBox="0 0 24 24" 
                      fill="none" 
                      stroke="currentColor" 
                      strokeWidth="1.5" 
                      className="lucide lucide-building"
                    >
                      <rect width="16" height="20" x="4" y="2" rx="2" ry="2"/>
                      <path d="M9 22v-4h6v4"/>
                      <path d="M8 6h.01"/>
                      <path d="M16 6h.01"/>
                      <path d="M12 6h.01"/>
                      <path d="M12 10h.01"/>
                      <path d="M12 14h.01"/>
                      <path d="M16 10h.01"/>
                      <path d="M16 14h.01"/>
                      <path d="M8 10h.01"/>
                      <path d="M8 14h.01"/>
                    </svg>
                    <div className="font-semibold text-base text-center leading-tight">I rent a home</div>
                  </Button>
                </div>
                {errors.housingStatus && (
                  <FormMessage variant="error" className="mt-2">{errors.housingStatus}</FormMessage>
                )}
              </FormField>
            </div>

            {/* Right Side - Conditional Forms */}
            <div className="col-span-8 border-l border-border pl-8">
              {formData.housingStatus && (
                <div className="space-y-6">
                  {/* Owner Details */}
                  {formData.housingStatus === 'own' && (
                    <div className="space-y-6">
                      <h3 className="text-xl font-semibold text-foreground border-b pb-2">
                        Homeowner Details
                      </h3>
                    
                    <FormField name="address">
                      <FormLabel htmlFor="address" required>
                        Property Address
                      </FormLabel>
                      <FormControl asChild>
                        <Input
                          id="address"
                          type="text"
                          value={formData.address || ''}
                          onChange={handleInputChange('address')}
                          placeholder="123 Main Street, City, State 12345"
                          variant={errors.address ? 'error' : 'default'}
                        />
                      </FormControl>
                      {errors.address && (
                        <FormMessage variant="error">{errors.address}</FormMessage>
                      )}
                    </FormField>

                    <FormField name="estimatedValue">
                      <FormLabel htmlFor="estimatedValue" required>
                        Estimated Value
                      </FormLabel>
                      <FormControl asChild>
                        <Input
                          id="estimatedValue"
                          type="number"
                          min="0"
                          step="1000"
                          value={formData.estimatedValue || ''}
                          onChange={handleInputChange('estimatedValue')}
                          placeholder="500000"
                          variant={errors.estimatedValue ? 'error' : 'default'}
                        />
                      </FormControl>
                      {errors.estimatedValue && (
                        <FormMessage variant="error">{errors.estimatedValue}</FormMessage>
                      )}
                    </FormField>

                    <FormField name="dateAcquired">
                      <FormLabel htmlFor="dateAcquired" required>
                        Date Acquired
                      </FormLabel>
                      <FormControl asChild>
                        <Input
                          id="dateAcquired"
                          type="date"
                          value={formData.dateAcquired || ''}
                          onChange={handleInputChange('dateAcquired')}
                          variant={errors.dateAcquired ? 'error' : 'default'}
                        />
                      </FormControl>
                      {errors.dateAcquired && (
                        <FormMessage variant="error">{errors.dateAcquired}</FormMessage>
                      )}
                    </FormField>

                  </div>
                )}

                {/* Renter Details */}
                {formData.housingStatus === 'rent' && (
                  <div className="space-y-6">
                    <h3 className="text-xl font-semibold text-foreground border-b pb-2">
                      Renter Details
                    </h3>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField name="rentPayment">
                        <FormLabel htmlFor="rentPayment" required>
                          Rent Payment Amount
                        </FormLabel>
                        <FormControl asChild>
                          <Input
                            id="rentPayment"
                            type="number"
                            min="0"
                            step="50"
                            value={formData.rentPayment || ''}
                            onChange={handleInputChange('rentPayment')}
                            placeholder="2500"
                            variant={errors.rentPayment ? 'error' : 'default'}
                          />
                        </FormControl>
                        {errors.rentPayment && (
                          <FormMessage variant="error">{errors.rentPayment}</FormMessage>
                        )}
                      </FormField>

                      <FormField name="rentInterval">
                        <FormLabel htmlFor="rentInterval" required>
                          Payment Interval
                        </FormLabel>
                        <FormControl asChild>
                          <Select
                            value={formData.rentInterval || ''}
                            onValueChange={(value) => handleFieldUpdate('rentInterval', value)}
                          >
                            <SelectTrigger className={errors.rentInterval ? 'border-error' : ''}>
                              <SelectValue placeholder="Select interval" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="weekly">Weekly</SelectItem>
                              <SelectItem value="monthly">Monthly</SelectItem>
                              <SelectItem value="yearly">Yearly</SelectItem>
                            </SelectContent>
                          </Select>
                        </FormControl>
                        {errors.rentInterval && (
                          <FormMessage variant="error">{errors.rentInterval}</FormMessage>
                        )}
                      </FormField>
                    </div>

                    {/* Annual Rent Cost */}
                    {(formData.rentPayment || 0) > 0 && formData.rentInterval && (
                      <div className="bg-accent/10 rounded-lg p-6 border border-accent/20">
                        <h4 className="text-base font-semibold text-foreground mb-3">Annual Housing Cost</h4>
                        <div className="text-2xl font-bold text-primary mb-2">
                          ${annualRentCost.toLocaleString()}
                        </div>
                        <p className="text-sm text-muted-foreground">
                          ${(formData.rentPayment || 0).toLocaleString()} {formData.rentInterval} × {formData.rentInterval === 'weekly' ? '52' : formData.rentInterval === 'monthly' ? '12' : '1'} {formData.rentInterval === 'yearly' ? '' : formData.rentInterval === 'weekly' ? 'weeks' : 'months'}
                        </p>
                      </div>
                    )}
                  </div>
                )}
                </div>
              )}

              {/* Empty state when no housing status selected */}
              {!formData.housingStatus && (
                <div className="flex items-center justify-center h-64 text-center">
                  <div className="text-muted-foreground">
                    <svg 
                      width="64" 
                      height="64" 
                      viewBox="0 0 24 24" 
                      fill="none" 
                      stroke="currentColor" 
                      strokeWidth="1" 
                      className="mx-auto mb-4 opacity-50"
                    >
                      <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
                      <polyline points="9,22 9,12 15,12 15,22"/>
                    </svg>
                    <p className="text-lg">Select your housing situation to continue</p>
                    <p className="text-sm mt-2">Choose from the options on the left</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </form>
      </Form>
    </div>
  )
}