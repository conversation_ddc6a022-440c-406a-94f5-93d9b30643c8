import { useState } from 'react'
import { Input, Form, FormField, FormLabel, FormControl, FormMessage } from '@finpro/ui'
import { PersonalInfoData } from '../../types'
import { OnboardingStepHeader } from '../OnboardingStepHeader'

interface PersonalInfoFormProps {
  initialData?: Partial<PersonalInfoData>
  onBack?: () => void
  onSave?: (data: Partial<PersonalInfoData>) => void
  onContinue: (data: PersonalInfoData) => void
}

export function PersonalInfoForm({ initialData, onBack, onSave, onContinue }: PersonalInfoFormProps) {
  const [formData, setFormData] = useState<PersonalInfoData>({
    firstName: initialData?.firstName || '',
    middleName: initialData?.middleName || '',
    lastName: initialData?.lastName || '',
    dateOfBirth: initialData?.dateOfBirth || '',
    maritalStatus: initialData?.maritalStatus || 'single'
  })
  
  const [errors, setErrors] = useState<Record<string, string>>({})

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}
    
    if (!formData.firstName.trim()) {
      newErrors.firstName = 'First name is required'
    } else if (formData.firstName.length < 2) {
      newErrors.firstName = 'Must be at least 2 characters'
    }
    
    if (!formData.lastName.trim()) {
      newErrors.lastName = 'Last name is required'
    } else if (formData.lastName.length < 2) {
      newErrors.lastName = 'Must be at least 2 characters'
    }
    
    if (!formData.dateOfBirth) {
      newErrors.dateOfBirth = 'Date of birth is required'
    }
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleInputChange = (field: keyof PersonalInfoData) => (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = e.target.value
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const handleFieldUpdate = (field: keyof PersonalInfoData, value: PersonalInfoData[keyof PersonalInfoData]) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // Clear error when field is updated
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const maritalStatusOptions = [
    { value: 'single', label: 'Single' },
    { value: 'married', label: 'Married' },
    { value: 'divorced', label: 'Divorced' },
    { value: 'widowed', label: 'Widow' },
    { value: 'separated', label: 'Separated' }
  ]

  const handleSave = () => {
    if (onSave) {
      onSave(formData)
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    handleContinue()
  }

  const handleContinue = () => {
    if (validateForm()) {
      onContinue(formData)
    }
  }

  return (
    <div>
      <OnboardingStepHeader
        title="Personal information"
        showBack={!!onBack}
        onBack={onBack}
        onSave={onSave ? handleSave : undefined}
        onContinue={handleContinue}
      />

      <Form asChild>
        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Name Section */}
          <div>
            <h2 className="text-lg font-semibold text-foreground mb-4">Name</h2>
            <p className="text-sm text-muted-foreground mb-6">
              Please provide your full legal name as it appears on your identification documents.
            </p>
            
            <div className="grid grid-cols-3 gap-6">
              <FormField name="firstName">
                <FormLabel htmlFor="firstName" className="text-sm font-medium text-muted-foreground">
                  FIRST
                </FormLabel>
                <FormControl asChild>
                  <Input
                    id="firstName"
                    value={formData.firstName}
                    onChange={handleInputChange('firstName')}
                    placeholder="First name"
                    variant={errors.firstName ? 'error' : 'default'}
                    className="mt-2"
                  />
                </FormControl>
                {errors.firstName && (
                  <FormMessage variant="error">{errors.firstName}</FormMessage>
                )}
              </FormField>

              <FormField name="middleName">
                <FormLabel htmlFor="middleName" className="text-sm font-medium text-muted-foreground">
                  MIDDLE
                </FormLabel>
                <FormControl asChild>
                  <Input
                    id="middleName"
                    value={formData.middleName}
                    onChange={handleInputChange('middleName')}
                    placeholder="Middle name"
                    className="mt-2"
                  />
                </FormControl>
              </FormField>

              <FormField name="lastName">
                <FormLabel htmlFor="lastName" className="text-sm font-medium text-muted-foreground">
                  LAST
                </FormLabel>
                <FormControl asChild>
                  <Input
                    id="lastName"
                    value={formData.lastName}
                    onChange={handleInputChange('lastName')}
                    placeholder="Last name"
                    variant={errors.lastName ? 'error' : 'default'}
                    className="mt-2"
                  />
                </FormControl>
                {errors.lastName && (
                  <FormMessage variant="error">{errors.lastName}</FormMessage>
                )}
              </FormField>
            </div>
          </div>

          {/* Birthday Section */}
          <div>
            <h2 className="text-lg font-semibold text-foreground mb-4">Birthday</h2>
            <p className="text-sm text-muted-foreground mb-6">
              Enter your date of birth to verify your age and identity.
            </p>
            
            <div className="max-w-xs">
              <FormField name="dateOfBirth">
                <FormLabel htmlFor="dateOfBirth" className="text-sm font-medium text-muted-foreground">
                  BIRTHDAY
                </FormLabel>
                <FormControl asChild>
                  <Input
                    id="dateOfBirth"
                    type="date"
                    value={formData.dateOfBirth}
                    onChange={handleInputChange('dateOfBirth')}
                    variant={errors.dateOfBirth ? 'error' : 'default'}
                    className="mt-2"
                  />
                </FormControl>
                {errors.dateOfBirth && (
                  <FormMessage variant="error">{errors.dateOfBirth}</FormMessage>
                )}
              </FormField>
            </div>
          </div>

          {/* Marital Status Section */}
          <div>
            <h2 className="text-lg font-semibold text-foreground mb-4">Current marital status</h2>
            <p className="text-sm text-muted-foreground mb-6">
              Select your current marital status for tax and financial planning purposes.
            </p>
            
            <div className="flex flex-wrap gap-3">
              {maritalStatusOptions.map((option) => (
                <button
                  key={option.value}
                  type="button"
                  onClick={() => handleFieldUpdate('maritalStatus', option.value)}
                  className={`
                    px-4 py-2 rounded-lg border text-sm font-medium transition-colors
                    ${formData.maritalStatus === option.value
                      ? 'bg-primary text-primary-foreground border-primary'
                      : 'bg-background text-foreground border-border hover:bg-accent'
                    }
                  `}
                >
                  {option.label}
                </button>
              ))}
            </div>
          </div>
        </form>
      </Form>
    </div>
  )
}