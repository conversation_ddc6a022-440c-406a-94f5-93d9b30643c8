import { useState } from 'react'
import { SvgIcon } from '@finpro/ui'
import { SituationData } from '../../types'
import { OnboardingStepHeader } from '../OnboardingStepHeader'

interface SituationFormProps {
  initialData?: Partial<SituationData>
  onBack?: () => void
  onSave?: (data: Partial<SituationData>) => void
  onContinue: (data: SituationData) => void
}

export function SituationForm({ initialData, onBack, onSave, onContinue }: SituationFormProps) {
  const [formData, setFormData] = useState<Partial<SituationData>>({
    selectedCharacteristics: initialData?.selectedCharacteristics || []
  })

  const characteristics = [
    { id: 'married', label: 'Married', icon: 'people' as const },
    { id: 'kids', label: 'Kids', icon: 'people' as const },
    { id: 'military', label: 'Military', icon: 'person' as const }, // No direct military icon available
    { id: 'student', label: 'Student', icon: 'person' as const }, // No direct student icon available
    { id: 'investments', label: 'Investments', icon: 'assets' as const },
    { id: 'business-owner', label: 'Business Owner', icon: 'financial-profile' as const },
    { id: 'retired', label: 'Retired', icon: 'budget' as const },
    { id: 'government', label: 'Government', icon: 'account' as const } // No direct government icon available
  ]

  const toggleCharacteristic = (characteristicId: string) => {
    const currentCharacteristics = formData.selectedCharacteristics || []
    const updated = currentCharacteristics.includes(characteristicId)
      ? currentCharacteristics.filter(id => id !== characteristicId)
      : [...currentCharacteristics, characteristicId]
    
    setFormData(prev => ({
      ...prev,
      selectedCharacteristics: updated
    }))
  }

  const handleSave = () => {
    if (onSave) {
      onSave(formData)
    }
  }

  const handleContinue = () => {
    onContinue(formData as SituationData)
  }

  return (
    <div>
      <OnboardingStepHeader
        title="Financial Situation"
        description="Tell us about your current situation"
        showBack={!!onBack}
        onBack={onBack}
        onSave={onSave ? handleSave : undefined}
        onContinue={handleContinue}
      />

      <div className="space-y-8">
        <div>
          <h2 className="text-lg font-semibold text-foreground mb-4">What describes your situation?</h2>
          <p className="text-sm text-muted-foreground mb-6">
            Select all that apply to help us provide better recommendations.
          </p>
          
          <div className="flex flex-wrap gap-3">
            {characteristics.map((characteristic) => (
              <label
                key={characteristic.id}
                className={`flex flex-col items-center justify-center px-2 py-2 rounded-lg border-2 transition-all duration-200 cursor-pointer w-[120px] h-[122px] ${
                  (formData.selectedCharacteristics || []).includes(characteristic.id)
                    ? 'border-primary bg-primary/10 text-primary shadow-md'
                    : 'border-border bg-background text-foreground hover:border-primary/30 hover:bg-accent/50'
                }`}
              >
                <input
                  type="checkbox"
                  checked={(formData.selectedCharacteristics || []).includes(characteristic.id)}
                  onChange={() => toggleCharacteristic(characteristic.id)}
                  className="sr-only"
                />
                <SvgIcon 
                  shape={characteristic.icon} 
                  width={52} 
                  height={52} 
                  className="mb-1 flex-shrink-0"
                  style={{ width: '52px', height: '52px' }}
                />
                <span className="text-xs font-medium text-center leading-tight">{characteristic.label}</span>
              </label>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}