'use client'

import { useRouter } from 'next/navigation'
import { OnboardingStepHeader } from '../components'
import { useOnboardingStore, useCanNavigate, useOnboardingNavigation } from '../hooks'

export default function LiabilitiesPage() {
  const router = useRouter()
  const { canGoBack } = useCanNavigate()
  const { getNextStepPath, getPreviousStepPath } = useOnboardingNavigation()
  const { markStepComplete } = useOnboardingStore()

  const handleSave = () => {
    console.log('Saving liabilities draft...')
    markStepComplete('liabilities')
  }

  const handleContinue = () => {
    const nextPath = getNextStepPath()
    if (nextPath) {
      router.push(nextPath)
    }
  }

  const handleBack = () => {
    const prevPath = getPreviousStepPath()
    if (prevPath) {
      router.push(prevPath)
    }
  }

  return (
    <div>
      <OnboardingStepHeader
        title="Liabilities"
        description="Debts and financial obligations"
        showBack={canGoBack}
        onBack={handleBack}
        onSave={handleSave}
        onContinue={handleContinue}
      />

      <div className="space-y-8">
        <div className="bg-blue-50 p-6 rounded-lg border border-blue-200">
          <h2 className="text-lg font-semibold mb-4">📊 Liabilities Form</h2>
          <p className="text-muted-foreground">
            This will contain form fields for credit card debt, student loans, auto loans, personal loans, and other debts.
          </p>
        </div>
      </div>
    </div>
  )
}