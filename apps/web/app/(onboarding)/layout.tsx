'use client'

import { useOnboardingStore, useOnboardingProgress, useOnboardingNavigation } from './hooks'
import { ONBOARDING_STEPS } from './constants'
import { HelpSidebar } from './components/HelpSidebar'
import { useRouter } from 'next/navigation'

function OnboardingLayoutContent({ children }: { children: React.ReactNode }) {
  const router = useRouter()
  const { currentStep } = useOnboardingNavigation()
  const completedSteps = useOnboardingStore(state => state.completedSteps)
  const progressPercentage = useOnboardingProgress()
  const setCurrentStep = useOnboardingStore(state => state.setCurrentStep)
  
  const goToStep = (stepId: typeof currentStep) => {
    const stepConfig = ONBOARDING_STEPS.find(s => s.id === stepId)
    if (stepConfig) {
      setCurrentStep(stepId)
      router.push(stepConfig.path)
    }
  }
  
  // Mock user data for KPIs
  const mockUserData = {
    assets: 125000,
    liabilities: 45000,
    netWorth: 80000
  }
  return (
    <div className="min-h-screen bg-background flex">
      {/* Left Sidebar */}
      <div 
        className="w-56 h-screen border-r border-border bg-card flex-shrink-0 overflow-y-auto"
        style={{
          background: `linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)`,
          backgroundImage: `linear-gradient(rgba(226, 232, 240, 0.3) 1px, transparent 1px), linear-gradient(90deg, rgba(226, 232, 240, 0.3) 1px, transparent 1px)`,
          backgroundSize: '20px 20px'
        }}
      >
        <div className="p-4 space-y-6">
          {/* Logo/Brand */}
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
              <span className="text-primary-foreground font-bold text-sm">FP</span>
            </div>
            <div>
              <div className="font-semibold text-foreground">FinPro</div>
              <div className="text-xs text-muted-foreground">Financial Platform</div>
            </div>
          </div>

          {/* Main Menu */}
          <div>
            <div className="text-xs font-medium text-muted-foreground mb-3 uppercase tracking-wide">
              MAIN MENU
            </div>
            <div className="space-y-1">
              <div className="flex items-center gap-3 px-3 py-2 rounded-lg bg-primary/10 text-primary border border-primary/20">
                <span className="text-lg">🏠</span>
                <div className="flex-1 min-w-0">
                  <div className="font-medium text-sm">Home</div>
                  <div className="text-xs text-muted-foreground truncate">Home information form</div>
                </div>
              </div>
              
              <div className="flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-accent/50 text-muted-foreground hover:text-foreground cursor-pointer">
                <span className="text-lg">📊</span>
                <div className="flex-1 min-w-0">
                  <div className="font-medium text-sm">Portfolio</div>
                  <div className="text-xs text-muted-foreground truncate">Your investment portfolio</div>
                </div>
              </div>
              
              <div className="flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-accent/50 text-muted-foreground hover:text-foreground cursor-pointer">
                <span className="text-lg">📈</span>
                <div className="flex-1 min-w-0">
                  <div className="font-medium text-sm">Analytics</div>
                  <div className="text-xs text-muted-foreground truncate">Performance analytics</div>
                </div>
              </div>
              
              <div className="flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-accent/50 text-muted-foreground hover:text-foreground cursor-pointer">
                <span className="text-lg">💼</span>
                <div className="flex-1 min-w-0">
                  <div className="font-medium text-sm">Investments</div>
                  <div className="text-xs text-muted-foreground truncate">Investment opportunities</div>
                </div>
              </div>
              
              <div className="flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-accent/50 text-muted-foreground hover:text-foreground cursor-pointer">
                <span className="text-lg">🏦</span>
                <div className="flex-1 min-w-0">
                  <div className="font-medium text-sm">Accounts</div>
                  <div className="text-xs text-muted-foreground truncate">Manage your accounts</div>
                </div>
              </div>
              
              <div className="flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-accent/50 text-muted-foreground hover:text-foreground cursor-pointer">
                <span className="text-lg">📋</span>
                <div className="flex-1 min-w-0">
                  <div className="font-medium text-sm">Reports</div>
                  <div className="text-xs text-muted-foreground truncate">Financial reports</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col min-h-screen">
        {/* Header */}
        <div className="border-b border-border bg-card">
          <div className="p-4 flex items-center justify-between">
            <div className="flex items-center gap-8">
              <div>
                <h1 className="text-xl font-semibold">Onboarding Progress</h1>
                <p className="text-sm text-muted-foreground">
                  Step {ONBOARDING_STEPS.findIndex(s => s.id === currentStep) + 1} of {ONBOARDING_STEPS.length} • {completedSteps.size} completed
                </p>
              </div>
              <div className="text-2xl font-bold text-primary">{progressPercentage}%</div>
            </div>
            
            {/* KPI Summary */}
            <div className="flex items-center gap-8 text-sm">
              <div className="text-center">
                <div className="text-xs text-muted-foreground mb-1">Assets</div>
                <div className="font-semibold text-foreground">${mockUserData.assets.toLocaleString()}</div>
              </div>
              <div className="text-center">
                <div className="text-xs text-muted-foreground mb-1">Liabilities</div>
                <div className="font-semibold text-foreground">${mockUserData.liabilities.toLocaleString()}</div>
              </div>
              <div className="text-center">
                <div className="text-xs text-muted-foreground mb-1">Net Worth</div>
                <div className="font-semibold text-primary">${mockUserData.netWorth.toLocaleString()}</div>
              </div>
            </div>
          </div>
          
          {/* Step Navigation */}
          <div className="px-4 border-t border-border">
            <div className="flex overflow-x-auto">
              {ONBOARDING_STEPS.map((step) => {
                const isCurrentStep = currentStep === step.id
                const isCompleted = completedSteps.has(step.id)
                
                return (
                  <div
                    key={step.id}
                    onClick={() => !isCurrentStep && goToStep(step.id)}
                    className={`
                      relative px-4 py-3 text-sm whitespace-nowrap border-b-2 transition-all duration-200
                      ${isCurrentStep
                        ? 'border-primary text-primary cursor-default' 
                        : isCompleted
                          ? 'border-green-500 text-foreground hover:text-primary hover:border-primary/30 cursor-pointer hover:bg-accent/20'
                          : 'border-transparent text-muted-foreground hover:text-foreground hover:border-primary/20 cursor-pointer hover:bg-accent/10'
                      }
                    `}
                    title={
                      isCurrentStep 
                        ? 'Current step' 
                        : `Click to go to ${step.title}`
                    }
                  >
                    {step.title}
                  </div>
                )
              })}
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 bg-background p-6">
          {children}
        </div>
      </div>

      {/* Right Sidebar - Help Content */}
      <div 
        className="w-80 h-screen border-l border-border bg-card flex-shrink-0"
        style={{
          background: `linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)`,
          backgroundImage: `linear-gradient(rgba(226, 232, 240, 0.3) 1px, transparent 1px), linear-gradient(90deg, rgba(226, 232, 240, 0.3) 1px, transparent 1px)`,
          backgroundSize: '20px 20px'
        }}
      >
        <HelpSidebar currentStep={currentStep} />
      </div>
    </div>
  )
}

export default function OnboardingLayout({ children }: { children: React.ReactNode }) {
  return (
    <OnboardingLayoutContent>
      {children}
    </OnboardingLayoutContent>
  )
}