# Onboarding State Management Migration Plan

## Overview
Migrate from complex React Context + custom hooks to simplified Zustand store with localStorage persistence. This will reduce complexity while maintaining functionality and ease future backend integration.

## Current State Analysis

### Files to Modify/Remove:
- `hooks/useOnboardingFlow.ts` - **REMOVE** (232 lines → 0)
- `hooks/index.ts` - **UPDATE** (export new store)
- `layout.tsx` - **SIMPLIFY** (remove OnboardingProvider)
- All page components - **UPDATE** (use new store)
- `components/forms/*` - **SIMPLIFY** (remove useOnboardingForm)

### Current Issues:
1. Dual state management (Context + local form state)
2. Complex localStorage persistence logic
3. Over-engineered form validation hooks
4. Manual progress calculations
5. Inconsistent usage patterns across components

## New Zustand Architecture

### 1. Store Structure
```typescript
// stores/onboarding.ts
interface OnboardingStore {
  // Data
  data: Partial<OnboardingFormData>
  completedSteps: Set<OnboardingStepId>
  currentStep: OnboardingStepId
  
  // Computed
  progressPercentage: number
  canGoNext: boolean
  canGoBack: boolean
  
  // Actions
  updateData: (section: keyof OnboardingFormData, data: any) => void
  markStepComplete: (step: OnboardingStepId) => void
  goToStep: (step: OnboardingStepId) => void
  goToNextStep: () => void
  goToPreviousStep: () => void
  resetOnboarding: () => void
}
```

### 2. Key Features
- **Automatic Persistence**: Built-in localStorage with Zustand persist middleware
- **URL Sync**: Navigation integrated with Next.js router
- **Computed Values**: Progress and navigation state derived automatically
- **Type Safety**: Full TypeScript support with existing types
- **Backend Ready**: Easy transition to API calls later

## Migration Steps

### Phase 1: Create New Store (30 mins)
1. **Create** `stores/onboarding.ts`
   - Implement Zustand store with persist middleware
   - Add navigation logic with router integration
   - Include all current functionality

2. **Add** necessary dependencies
   ```bash
   # Zustand already installed - verify persist middleware
   npm list zustand
   ```

### Phase 2: Update Core Files (45 mins)
3. **Update** `hooks/index.ts`
   - Export new store instead of old hooks
   - Keep existing exports for backward compatibility during migration

4. **Simplify** `layout.tsx`
   - Remove OnboardingProvider wrapper
   - Update to use Zustand store directly
   - Keep existing UI structure intact

### Phase 3: Migrate Pages (60 mins)
5. **Update** each page component:
   - `personal-info/page.tsx` - Use store instead of context
   - `situation/page.tsx` - Use store instead of context
   - `home/page.tsx` - Use store instead of context
   - `assets/page.tsx` - Use store instead of context
   - `liabilities/page.tsx` - Use store instead of context
   - `insurance/page.tsx` - Use store instead of context
   - `income/page.tsx` - Use store instead of context
   - `expenses/page.tsx` - Use store instead of context
   - `review/page.tsx` - Use store instead of context

### Phase 4: Simplify Forms (45 mins)
6. **Refactor** form components:
   - `PersonalInfoForm.tsx` - Remove useOnboardingForm, use native form state
   - `SituationForm.tsx` - Remove useOnboardingForm, use native form state
   - `HomeForm.tsx` - Remove useOnboardingForm, use native form state
   - Keep validation logic but simplify implementation

### Phase 5: Cleanup (15 mins)
7. **Remove** old files:
   - Delete `hooks/useOnboardingFlow.ts`
   - Clean up any unused imports
   - Update `components/index.ts` exports

## Implementation Details

### New Store Implementation
```typescript
// stores/onboarding.ts
import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { useRouter } from 'next/navigation'
import { ONBOARDING_STEPS } from '../constants'

interface OnboardingStore {
  // Core state
  data: Partial<OnboardingFormData>
  completedSteps: Set<OnboardingStepId>
  currentStep: OnboardingStepId
  
  // Actions
  updateData: (section: keyof OnboardingFormData, data: any) => void
  markStepComplete: (step: OnboardingStepId) => void
  setCurrentStep: (step: OnboardingStepId) => void
  goToNextStep: () => void
  goToPreviousStep: () => void
  resetOnboarding: () => void
}

export const useOnboardingStore = create<OnboardingStore>()(
  persist(
    (set, get) => ({
      // Initial state
      data: {},
      completedSteps: new Set(),
      currentStep: 'personal-info',
      
      // Actions
      updateData: (section, data) => 
        set(state => ({ data: { ...state.data, [section]: data } })),
      
      markStepComplete: (step) =>
        set(state => ({ completedSteps: new Set([...state.completedSteps, step]) })),
      
      setCurrentStep: (step) => set({ currentStep: step }),
      
      goToNextStep: () => {
        const { currentStep } = get()
        const currentIndex = ONBOARDING_STEPS.findIndex(s => s.id === currentStep)
        if (currentIndex < ONBOARDING_STEPS.length - 1) {
          const nextStep = ONBOARDING_STEPS[currentIndex + 1]
          set({ currentStep: nextStep.id })
          // Router navigation handled in components
        }
      },
      
      goToPreviousStep: () => {
        const { currentStep } = get()
        const currentIndex = ONBOARDING_STEPS.findIndex(s => s.id === currentStep)
        if (currentIndex > 0) {
          const prevStep = ONBOARDING_STEPS[currentIndex - 1]
          set({ currentStep: prevStep.id })
          // Router navigation handled in components
        }
      },
      
      resetOnboarding: () => set({ 
        data: {}, 
        completedSteps: new Set(), 
        currentStep: 'personal-info' 
      })
    }),
    {
      name: 'onboarding-storage',
      // Custom serialization for Set
      serialize: (state) => JSON.stringify({
        ...state,
        completedSteps: Array.from(state.completedSteps)
      }),
      deserialize: (str) => {
        const parsed = JSON.parse(str)
        return {
          ...parsed,
          completedSteps: new Set(parsed.completedSteps)
        }
      }
    }
  )
)

// Computed selectors
export const useOnboardingProgress = () => {
  const completedSteps = useOnboardingStore(state => state.completedSteps)
  return Math.round((completedSteps.size / ONBOARDING_STEPS.length) * 100)
}

export const useCanNavigate = () => {
  const currentStep = useOnboardingStore(state => state.currentStep)
  const currentIndex = ONBOARDING_STEPS.findIndex(s => s.id === currentStep)
  
  return {
    canGoNext: currentIndex < ONBOARDING_STEPS.length - 1,
    canGoBack: currentIndex > 0
  }
}
```

### Updated Page Pattern
```typescript
// personal-info/page.tsx
'use client'
import { useRouter } from 'next/navigation'
import { useOnboardingStore, useCanNavigate } from '../stores/onboarding'
import { PersonalInfoForm } from '../components'

export default function PersonalInfoPage() {
  const router = useRouter()
  const { canGoBack, canGoNext } = useCanNavigate()
  const { data, updateData, markStepComplete, goToNextStep, goToPreviousStep } = useOnboardingStore()

  const handleSave = (formData: PersonalInfoData) => {
    updateData('personalInfo', formData)
    markStepComplete('personal-info')
  }

  const handleContinue = (formData: PersonalInfoData) => {
    updateData('personalInfo', formData)
    markStepComplete('personal-info')
    goToNextStep()
    
    // Navigate to next step
    const currentIndex = ONBOARDING_STEPS.findIndex(s => s.id === 'personal-info')
    const nextStep = ONBOARDING_STEPS[currentIndex + 1]
    router.push(nextStep.path)
  }

  const handleBack = () => {
    goToPreviousStep()
    const currentIndex = ONBOARDING_STEPS.findIndex(s => s.id === 'personal-info')
    const prevStep = ONBOARDING_STEPS[currentIndex - 1]
    router.push(prevStep.path)
  }

  return (
    <PersonalInfoForm
      initialData={data.personalInfo}
      onBack={canGoBack ? handleBack : undefined}
      onSave={handleSave}
      onContinue={handleContinue}
    />
  )
}
```

### Simplified Form Pattern
```typescript
// Remove useOnboardingForm, use standard React patterns
'use client'
import { useState } from 'react'

interface PersonalInfoFormProps {
  initialData?: Partial<PersonalInfoData>
  onSave?: (data: PersonalInfoData) => void
  onContinue: (data: PersonalInfoData) => void
  onBack?: () => void
}

export function PersonalInfoForm({ initialData, onSave, onContinue, onBack }: PersonalInfoFormProps) {
  const [formData, setFormData] = useState<PersonalInfoData>({
    firstName: initialData?.firstName || '',
    middleName: initialData?.middleName || '',
    lastName: initialData?.lastName || '',
    dateOfBirth: initialData?.dateOfBirth || '',
    maritalStatus: initialData?.maritalStatus || 'single'
  })
  
  const [errors, setErrors] = useState<Record<string, string>>({})

  const validateAndSubmit = (action: 'save' | 'continue') => {
    const newErrors: Record<string, string> = {}
    
    if (!formData.firstName.trim()) newErrors.firstName = 'First name is required'
    if (!formData.lastName.trim()) newErrors.lastName = 'Last name is required'
    if (!formData.dateOfBirth) newErrors.dateOfBirth = 'Date of birth is required'
    
    setErrors(newErrors)
    
    if (Object.keys(newErrors).length === 0) {
      if (action === 'save' && onSave) {
        onSave(formData)
      } else if (action === 'continue') {
        onContinue(formData)
      }
    }
  }

  // Rest of form implementation...
}
```

## Benefits After Migration

### Immediate Benefits:
- **-70% Code Reduction**: ~300 lines → ~90 lines
- **Better Performance**: No unnecessary re-renders from Context
- **Simpler Mental Model**: Single source of truth
- **Type Safety**: Better TypeScript integration

### Future Benefits:
- **Easy Backend Integration**: Just swap action implementations
- **Better Debugging**: Zustand DevTools support
- **Offline Support**: Built-in persistence
- **Scalability**: Easy to add new features

## Testing Plan

1. **Manual Testing**: Go through entire onboarding flow
2. **Data Persistence**: Refresh browser, verify data persists
3. **Navigation**: Test back/forward, direct URL access
4. **Form Validation**: Test all validation scenarios
5. **Edge Cases**: Clear storage, incomplete flows

## Rollback Plan

If issues arise, keep the old files in a backup folder until migration is complete and tested. The migration can be done incrementally, keeping both systems working simultaneously.

## Timeline Estimate

**Total: 3-4 hours**
- Phase 1: 30 mins
- Phase 2: 45 mins  
- Phase 3: 60 mins
- Phase 4: 45 mins
- Phase 5: 15 mins
- Testing: 30 mins

## Future Backend Integration

Once backend API is ready, migration is simple:

```typescript
// Just update the actions in the store
updateData: async (section, data) => {
  // Optimistic update
  set(state => ({ data: { ...state.data, [section]: data } }))
  
  try {
    await api.post('/onboarding', { section, data })
  } catch (error) {
    // Revert on error
    set(state => ({ data: previousState }))
    throw error
  }
}
```

This migration will significantly simplify the codebase while maintaining all current functionality and making future backend integration seamless.