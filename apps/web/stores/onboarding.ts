'use client'

import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { OnboardingStepId, OnboardingFormData } from '../app/(onboarding)/types'
import { ONBOARDING_STEPS } from '../app/(onboarding)/constants'

interface OnboardingStore {
  // Core state
  data: Partial<OnboardingFormData>
  completedSteps: Set<OnboardingStepId>
  currentStep: OnboardingStepId
  
  // Actions
  updateData: (section: keyof OnboardingFormData, data: any) => void
  markStepComplete: (step: OnboardingStepId) => void
  setCurrentStep: (step: OnboardingStepId) => void
  goToNextStep: () => void
  goToPreviousStep: () => void
  resetOnboarding: () => void
}

export const useOnboardingStore = create<OnboardingStore>()(
  persist(
    (set, get) => ({
      // Initial state
      data: {},
      completedSteps: new Set<OnboardingStepId>(),
      currentStep: 'personal-info',
      
      // Actions
      updateData: (section, data) => 
        set(state => ({ 
          data: { ...state.data, [section]: data } 
        })),
      
      markStepComplete: (step) =>
        set(state => ({ 
          completedSteps: new Set([...Array.from(state.completedSteps), step]) 
        })),
      
      setCurrentStep: (step) => set({ currentStep: step }),
      
      goToNextStep: () => {
        const { currentStep } = get()
        const currentIndex = ONBOARDING_STEPS.findIndex(s => s.id === currentStep)
        if (currentIndex < ONBOARDING_STEPS.length - 1) {
          const nextStep = ONBOARDING_STEPS[currentIndex + 1]
          set({ currentStep: nextStep.id })
        }
      },
      
      goToPreviousStep: () => {
        const { currentStep } = get()
        const currentIndex = ONBOARDING_STEPS.findIndex(s => s.id === currentStep)
        if (currentIndex > 0) {
          const prevStep = ONBOARDING_STEPS[currentIndex - 1]
          set({ currentStep: prevStep.id })
        }
      },
      
      resetOnboarding: () => set({ 
        data: {}, 
        completedSteps: new Set<OnboardingStepId>(), 
        currentStep: 'personal-info' 
      })
    }),
    {
      name: 'onboarding-storage',
      // Custom storage to handle Set serialization
      storage: {
        getItem: (name) => {
          const str = localStorage.getItem(name)
          if (!str) return null
          try {
            const parsed = JSON.parse(str)
            return {
              ...parsed,
              state: {
                ...parsed.state,
                completedSteps: new Set(parsed.state.completedSteps || [])
              }
            }
          } catch {
            return null
          }
        },
        setItem: (name, value) => {
          const serialized = {
            ...value,
            state: {
              ...value.state,
              completedSteps: Array.from(value.state.completedSteps)
            }
          }
          localStorage.setItem(name, JSON.stringify(serialized))
        },
        removeItem: (name) => localStorage.removeItem(name)
      }
    }
  )
)

// Computed selectors
export const useOnboardingProgress = () => {
  const completedSteps = useOnboardingStore(state => state.completedSteps)
  return Math.round((completedSteps.size / ONBOARDING_STEPS.length) * 100)
}

export const useCanNavigate = () => {
  const currentStep = useOnboardingStore(state => state.currentStep)
  const currentIndex = ONBOARDING_STEPS.findIndex(s => s.id === currentStep)
  
  return {
    canGoNext: currentIndex < ONBOARDING_STEPS.length - 1,
    canGoBack: currentIndex > 0
  }
}

// Navigation helpers - these will work with Next.js router
export const useOnboardingNavigation = () => {
  const { currentStep, goToNextStep, goToPreviousStep } = useOnboardingStore()
  const { canGoNext, canGoBack } = useCanNavigate()
  
  const getNextStepPath = () => {
    if (!canGoNext) return null
    const currentIndex = ONBOARDING_STEPS.findIndex(s => s.id === currentStep)
    return ONBOARDING_STEPS[currentIndex + 1]?.path
  }
  
  const getPreviousStepPath = () => {
    if (!canGoBack) return null
    const currentIndex = ONBOARDING_STEPS.findIndex(s => s.id === currentStep)
    return ONBOARDING_STEPS[currentIndex - 1]?.path
  }
  
  const getCurrentStepPath = () => {
    const step = ONBOARDING_STEPS.find(s => s.id === currentStep)
    return step?.path || '/personal-info'
  }
  
  return {
    currentStep,
    canGoNext,
    canGoBack,
    goToNextStep,
    goToPreviousStep,
    getNextStepPath,
    getPreviousStepPath,
    getCurrentStepPath
  }
}